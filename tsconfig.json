{
  "compilerOptions": {
    "target": "es2021",
    "lib": ["es2021"],
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "esModuleInterop": false, // If true, testing.spec.ts doesn't work
    "outDir": "lib",
    "allowSyntheticDefaultImports": true,
    "sourceMap": true,
    "isolatedModules": false,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "declaration": false,
    "noImplicitAny": false,
    "removeComments": true,
    "noLib": false,
    "preserveConstEnums": true,
    "inlineSources": false,
    "skipLibCheck": true,
    "ignoreDeprecations": "5.0",
    "useDefineForClassFields": false
  },
  "include": [
    "src/**/*.ts",
    "resources"
  ],
  "exclude": [
    "node_modules"
  ]
}
