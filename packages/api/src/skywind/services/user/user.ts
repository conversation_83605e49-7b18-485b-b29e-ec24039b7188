import { UserModel } from "../../models/user";
import {
    AuthInfo,
    DetailedUserInfo,
    ForcePasswordChangePeriodType,
    PermissionsList,
    UpdateStatusesData,
    User,
    UserCustomData,
    UserInfo,
    UserPermissions,
    UserStatus,
    UserType,
    UserTypeInfo,
    UserWithBlockingInfo
} from "../../entities/user";
import * as Errors from "../../errors";
import { BaseEntity, Entity } from "../../entities/entity";
import { FindOptions, literal, Op, Transaction, WhereOptions } from "sequelize";
import { sequelize as db } from "../../storage/db";
import * as SecurityService from "../security";
import { buildKey, createResetToken, SECURITY_AUTH_TYPE, verifyResetToken } from "../security";
import * as PermissionService from "../permission";
import * as EntityService from "../entity";
import { EntityImpl, getChildIds } from "../entity";
import { EmailData } from "../../utils/emails";
import * as Settings from "../settings";
import { getEntitiesSettings } from "../settings";
import { EntitySettings } from "../../entities/settings";
import { PagingHelper } from "../../utils/paginghelper";
import { RoleModel } from "../../models/role";
import { addRolesToUser, getPermissions, RoleImpl } from "../role";
import { Role, RoleInfo } from "../../entities/role";
import { getSortKey, valueFromQuery } from "../filter";
import { SUPER_ADMIN_ID, SUPER_ADMIN_ROLE_ID, SUPERADMIN_PASSWORD, SUPERADMIN_USERNAME } from "../../utils/common";
import * as redis from "../../storage/redis";
import EntityCache from "../../cache/entity";
import * as TokenUtils from "../../utils/token";
import config from "../../config";
import { getEmailService } from "../email";
import { Models } from "../../models/models";

const uModel = Models.UserModel;
const rlModel = Models.RoleModel;

const GROUP_ACTION_MAX_ITEMS: number = 100;

export interface CreateData {
    username: string;
    firstName?: string;
    lastName?: string;
    password: string;
    email?: string;
    phone?: string;
    roles?: RoleInfo[];
    editorEntity?: BaseEntity;
    userType?: UserType;
    forcePasswordChangePeriodType?: ForcePasswordChangePeriodType;
    forcePasswordChangePeriod?: number;
    customData?: UserCustomData;
    status?: UserStatus;
}

export type UpdateData = Partial<Omit<CreateData, "password" | "email">>;

export interface PasswordHistoryRecord {
    salt: string;
    password: string;
    createdAt: Date;
}

export interface DirectChangeEmailData {
    email: string;
}

export interface ChangeEmailData {
    token: string;
    newEmail: string;
    domain: string;
}

export const queryParamsKeys = [
    "userId",
    "username",
    "firstName",
    "lastName",
    "email",
    "lastLogin",
    "passwordChangedAt",
    "createdAt",
    "updatedAt",
    "status",
    "entity",
    "userType",
    "roleId",
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
    "customData",
    "customDataRoleId",
];
const sortableKeys = [
    "fullName",
    "firstName",
    "lastName",
    "email",
    "username",
    "lastLogin",
    "createdAt",
    "status",
    "entity",
    "userType",
    "roleId",
];
const sortByMapper = {
    "entity": "entity.path"
};
const DEFAULT_SORT_KEY = "username";

const CUSTOM_DATA_FILTER_KEYS = ["nickname", "cardNumber", "dateOfBirth", "group", "role"];

export class UserImpl implements User {
    public id: number;
    public entityId: number;
    public username: string;
    public firstName: string;
    public lastName: string;
    public email: string;
    public phone: string;
    public status: UserStatus;
    public salt: string;
    public password: string;
    public grantedPermissions: PermissionsList;
    public entity: BaseEntity;
    public roles?: Role[];
    public rolesInfo?: RoleInfo[];
    public lastLogin: Date;
    public passwordChangedAt: Date;
    public createdAt: Date;
    public updatedAt: Date;
    public defaultTwoFAType?: SECURITY_AUTH_TYPE;
    public authInfo?: AuthInfo;
    public passwordHistory: PasswordHistoryRecord[];
    public userType?: UserType;
    public customData: UserCustomData;

    public version: number = 0;

    constructor(item?: UserModel) {
        if (!item) {
            return;
        }

        this.id = item.get("id");
        this.entityId = item.get("entityId");
        this.username = item.get("username");
        this.firstName = item.get("firstName");
        this.lastName = item.get("lastName");
        this.salt = item.get("salt");
        this.password = item.get("password");
        this.email = item.get("email");
        this.phone = item.get("phone");
        this.status = item.get("status");
        this.userType = item.get("userType");
        this.passwordHistory = item.get("passwordHistory");
        this.version = item.get("version");
        this.lastLogin = item.get("lastLogin");
        this.passwordChangedAt = item.get("passwordChangedAt");
        this.createdAt = item.get("createdAt");
        this.updatedAt = item.get("updatedAt");
        this.customData = item.get("customData") || {};

        const entityDB = item.get("entity");
        if (entityDB) {
            this.entity = new EntityImpl(entityDB);
        }

        const rolesDB = item.get("roles");
        if (rolesDB) {
            this.rolesInfo = [];
            this.roles = rolesDB.map((role: RoleModel) => {
                const roleImpl = new RoleImpl(role);
                this.rolesInfo.push(roleImpl.toInfo());
                return roleImpl;
            });

            this.grantedPermissions = getPermissions(this.roles);
        }

        this.authInfo = item.get("authInfo");
        if (this.authInfo && this.authInfo.defaultAuthType) {
            this.defaultTwoFAType = this.authInfo.defaultAuthType;
        }
    }

    public isSuspended(): boolean {
        return this.status === UserStatus.SUSPENDED;
    }

    public async save(): Promise<UserInfo> {
        const oldVersion = this.version;
        this.version = oldVersion + 1;

        try {
            const [affectedCount] = await uModel.update(this, {
                where: {
                    id: this.id,
                    version: oldVersion,
                },
            });
            if (affectedCount !== 1) {
                return Promise.reject(new Errors.OptimisticLockException());
            }
        } catch (err) {
            this.version = oldVersion;
            return Promise.reject(err);
        }

        return new UserInfoImpl(this);
    }

    public toInfo(): UserInfo {
        return {
            username: this.username,
            status: this.status,
        };
    }

    public toDetailedInfo(keyEntity?: BaseEntity): DetailedUserInfo {
        let entity: string = this.entity ? this.entity.name : undefined;
        entity = keyEntity && entity ? this.entity.path.replace(keyEntity.path, "") : undefined;
        const result = Object.assign(this.toInfo(), {
            email: this.email,
            id: this.id,
            firstName: this.firstName,
            lastName: this.lastName,
            entity: entity,
            roles: this.roles.map((role: RoleImpl) => role.toEncodedInfo()),
            grantedPermissions: this.grantedPermissions,
            lastLogin: this.lastLogin,
            passwordChangedAt: this.passwordChangedAt,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            phone: this.phone,
            userType: this.userType,
            customData: this.customData
        });

        if (this.authInfo) {
            if (this.authInfo.defaultAuthType) {
                result["twoFAInfo"] = {
                    defaultAuthType: this.authInfo.defaultAuthType, authTypes: this.authInfo.authTypes
                };
            }

            if (this.authInfo.forcePasswordChangePeriod) {
                result["forcePasswordChangePeriod"] = this.authInfo.forcePasswordChangePeriod;
                result["forcePasswordChangePeriodType"] = this.authInfo.forcePasswordChangePeriodType;
            }
        }

        return result;
    }

    public async toProfileInfo(keyEntity: BaseEntity): Promise<UserWithBlockingInfo> {
        const userInfo: DetailedUserInfo = this.toDetailedInfo(keyEntity);
        const now: Date = new Date();

        return redis.usingDb<UserWithBlockingInfo>(async (db) => {
            const keyForChangePasswordFails = buildKey("changePasswordAttempts", keyEntity.key, this.username);
            const changePasswordBlockTimeout =  await db.ttl(keyForChangePasswordFails);

            let changePasswordTillDate: Date = null;
            if (changePasswordBlockTimeout && changePasswordBlockTimeout > 0) {
                changePasswordTillDate = new Date();
                changePasswordTillDate.setSeconds(now.getSeconds() + changePasswordBlockTimeout);
            }

            return {
                ...userInfo,
                blocking: {
                    changePasswordTillDate
                }
            };
        });
    }

    public isSuperAdmin(): boolean {
        return this.id === SUPER_ADMIN_ID;
    }

    public hasSuperAdminRole(): boolean {
        return this.hasRole(SUPER_ADMIN_ROLE_ID) || this.hasRole(config.security.superAdminRoleId) || false;
    }

    private hasRole(roleId: number): boolean {
        return this.roles && roleId && this.roles.some(role => role.id === roleId);
    }

    public tfaIsConfigured(): boolean {
        const numberOfTypes = this?.authInfo?.authTypes?.length;
        return !!numberOfTypes;
    }

    public hasPhone(): boolean {
        return this.phone !== undefined && this.phone !== null;
    }

    public generateTFAToken(): Promise<string> {
        return TokenUtils.generateTwoFAToken({
            userId: this.id,
            entityId: this.entityId,
            username: this.username
        });
    }
}

export class UserInfoImpl implements UserInfo {
    public username: string;
    public status: string;

    constructor(item?) {
        if (!item) {
            return;
        }
        this.username = item.username;
        this.status = item.status;
    }
}

/**
 * Create database structure to for UserModel
 */
export async function initDB(): Promise<void> {
    const entity = await EntityService.findOne({ name: "MASTER" });
    const service = getUserService(entity);

    try {
        await service.findOne("SUPERADMIN");
    } catch (err) {
        if (err instanceof Errors.UserNotExist) {
            let role = await rlModel.findOne({
                where: {
                    entityId: entity.id,
                    title: "SUPERADMIN"
                }
            });
            if (!role) {
                const permissions = await PermissionService.getPermissionsDescriptions();
                const newRole = new RoleImpl();
                newRole.entityId = entity.id;
                newRole.title = "SUPERADMIN";
                newRole.permissions = permissions.map(x => x.code).sort();
                newRole.isShared = false;
                role = await rlModel.create(newRole);
            }

            await service.create({
                username: SUPERADMIN_USERNAME,
                password: SUPERADMIN_PASSWORD,
                email: "<EMAIL>",
                roles: [new RoleImpl(role).toEncodedInfo()],
            });

        } else {
            return Promise.reject(err);
        }
    }
}

/**
 * @param isKeyEntity - param to indicate that service can set roles on user creation or update them on user patch
 */
export default function getUserService(entity: BaseEntity, canEditRoles?: boolean) {
    return new UserServiceImpl(entity, canEditRoles);
}

export interface UserService {
    create(data: CreateData): Promise<UserInfo>;

    update(username: string, data: UpdateData): Promise<UserInfo>;

    findOne(username: string): Promise<User>;

    getPermissionList(username: string): Promise<UserPermissions>;

    suspend(username: string): Promise<UserInfo>;

    restore(username: string): Promise<UserInfo>;

    remove(username: string): Promise<void>;
}

class UserServiceImpl implements UserService {
    constructor(public entity: BaseEntity, public canEditRoles: boolean = true) {
    }

    public async create(data: CreateData): Promise<UserInfo> {
        this.raiseErrorIfSuspended();

        await this.validateCreateData(data);

        const user = await this.createUser(data, this.canEditRoles);

        return user.toInfo();
    }

    public async update(username: string, data: UpdateData): Promise<UserInfo> {
        this.raiseErrorIfSuspended();

        let user: User = await this.findOne(username);

        user = await this.patchUser(user, data, this.canEditRoles);

        try {
            return await user.save();
        } catch (err) {
            if (err.name === "SequelizeUniqueConstraintError") {
                return Promise.reject(new Errors.UserAlreadyExistError());
            }
            return Promise.reject(err);
        }
    }

    public async getPermissionList(username: string): Promise<UserPermissions> {
        const user = await this.findOne(username);
        return { grantedPermissions: user.grantedPermissions };
    }

    public suspend(username: string): Promise<UserInfo> {
        return this.changeStatus(username, UserStatus.SUSPENDED);
    }

    public restore(username: string): Promise<UserInfo> {
        return this.changeStatus(username, UserStatus.NORMAL);
    }

    private async changeStatus(username: string, status: UserStatus): Promise<UserInfo> {
        this.raiseErrorIfSuspended();

        const user: User = await this.findOne(username);
        user.status = status;
        return user.save();
    }

    public async changeUserTypeByPath(username: string, type: UserType): Promise<UserTypeInfo> {
        try {
            this.raiseErrorIfSuspended();
            const user: User = await this.findOne(username);
            if (type === UserType.STUDIO) {
                throw new Errors.ChangeUserTypeToStudioUserError();
            }
            if (user.userType === UserType.STUDIO) {
                throw new Errors.ChangeUserTypeWhenStudioUserTypeHasError();
            }
            await this.changeUserType(user, type);
        } catch (error) {
            return Promise.reject(error);
        }
        return { userType: type };
    }

    private async changeUserType(user: User, type: UserType): Promise<UserInfo> {
        user.userType = type;
        return user.save();
    }

    private raiseErrorIfSuspended() {
        if (this.entity.isSuspended()) {
            throw new Errors.ParentSuspendedError();
        }
    }

    private async patchUser(user: User, data: UpdateData, canEditRoles: boolean): Promise<User> {

        this.decorateUserWithUpdateData(user, data);

        if (data.roles && canEditRoles) {
            await db.transaction(async (transaction: Transaction): Promise<any> => {
                await addRolesToUser(this.entity,
                    user.id,
                    data.roles,
                    data.editorEntity,
                    true,
                    transaction);
            });
        }
        return user;
    }

    private decorateUserWithUpdateData(user: User, data: UpdateData): void {
        if (data.username) {
            user.username = data.username;
        }

        if (data.firstName) {
            user.firstName = data.firstName;
        }

        if (data.lastName) {
            user.lastName = data.lastName;
        }

        if (data.status) {
            user.status = data.status;
        }

        if (data.phone !== undefined) {
            user.phone = data.phone;
        }

        if (data.userType) {
            user.userType = data.userType;
        } else if (!user.userType) {
            user.userType = UserType.OPERATOR_API;
        }

        if (data.forcePasswordChangePeriodType && data.forcePasswordChangePeriod) {
            if (user.authInfo) {
                user.authInfo.forcePasswordChangePeriod = +data.forcePasswordChangePeriod;
                user.authInfo.forcePasswordChangePeriodType = data.forcePasswordChangePeriodType;
            } else {
                user.authInfo = {
                    forcePasswordChangePeriodType: data.forcePasswordChangePeriodType,
                    forcePasswordChangePeriod: +data.forcePasswordChangePeriod
                };
            }
        }

        if (data.customData) {
            user.customData = data.customData;
        }
    }

    public async validateCreateData(data: CreateData) {
        if (data.username === data.password) {
            throw new Errors.SamePasswordAndUsernameError();
        }

        if (data.userType &&
            !Object.values(UserType).includes(data.userType)) {
            throw new Errors.InvalidUserType();
        }

        if (data.status &&
            !Object.values(UserStatus).includes(data.status)) {
            throw new Errors.ValidationError("Incorrect user status");
        }

        await this.validateDuplicateNamesInSubtree(data.username);
    }

    private async validateDuplicateNamesInSubtree(username: string) {
        const entitiesSettings = await getEntitiesSettings(this.entity.path);
        const currentEntityMergedSettings = entitiesSettings[this.entity.path];

        // if after merging settings current entity has unique flag then find parent which set it
        if (currentEntityMergedSettings.uniqueUsernamesInSubtree && username) {
            const entityPathWithUniqueSetting: string = Object.keys(entitiesSettings)
                .find(entityName => entitiesSettings[entityName].uniqueUsernamesInSubtree);

            const entityWithUniqueSetting = await EntityCache.findOne<Entity>({
                path: entityPathWithUniqueSetting
            });

            if (!entityWithUniqueSetting) {
                return;
            }

            const entityIds: number[] = [entityWithUniqueSetting.id, ...getChildIds(entityWithUniqueSetting)];
            const duplicatedUser = await uModel.findOne({
                where: {
                    username,
                    entityId: { [Op.in]: entityIds }
                }
            });

            if (duplicatedUser) {
                throw new Errors.UserAlreadyExistError();
            }
        }
    }

    private async createUser(data: CreateData, canEditRoles: boolean): Promise<User> {

        const user = await this.createDataToUserImpl(data);

        try {
            await db.transaction(async (transaction: Transaction): Promise<any> => {
                const item = await uModel.create(user, { transaction });

                if (data.roles && canEditRoles) {
                    await addRolesToUser(this.entity,
                        item.get("id"),
                        data.roles,
                        data.editorEntity,
                        false,
                        transaction);
                }
            });
        } catch (err) {
            if (err.name === "SequelizeUniqueConstraintError") {
                return Promise.reject(new Errors.UserAlreadyExistError());
            }
            // continue with the unknown error
            return Promise.reject(err);
        }

        return this.findOne(data.username);
    }

    private async createDataToUserImpl(data: CreateData): Promise<UserImpl> {
        const { salt, password } = await SecurityService.createSaltAndPassword(data.password);

        const user = new UserImpl();
        user.entityId = this.entity.id;
        user.username = data.username;
        user.firstName = data.firstName;
        user.lastName = data.lastName;
        user.email = data.email;
        user.phone = data.phone;
        user.salt = salt;
        user.password = password;
        user.status = data.status || UserStatus.NORMAL;
        user.passwordHistory = [{ password, salt, createdAt: new Date() }];
        user.userType = data.userType || UserType.OPERATOR_API;
        user.customData = data.customData || {};

        if (data.forcePasswordChangePeriodType && data.forcePasswordChangePeriod) {
            user.authInfo = {
                forcePasswordChangePeriodType: data.forcePasswordChangePeriodType,
                forcePasswordChangePeriod: +data.forcePasswordChangePeriod
            };
        }
        return user;
    }

    public async findOne(username: string, withoutPermissions?: boolean): Promise<User> {
        const query: WhereOptions = {
            entityId: this.entity.id,
            username: username,
        };
        const findOptions: FindOptions = { where: query };
        if (!withoutPermissions) {
            findOptions.include = [{ model: rlModel }];
        }

        const item = await uModel.findOne(findOptions);
        if (!item) {
            return Promise.reject(new Errors.UserNotExist());
        }
        return new UserImpl(item);
    }

    public async remove(username: string): Promise<void> {
        this.raiseErrorIfSuspended();

        const user = await this.findOne(username, true);

        if (user.isSuperAdmin()) {
            return Promise.reject(new Errors.ValidationError("You cannot delete superadmin"));
        }

        await uModel.destroy({
            where: {
                id: user.id
            }
        });
    }
}

export async function updateStatuses(entity: BaseEntity, data: UpdateStatusesData): Promise<void> {
    if (data.id.length > GROUP_ACTION_MAX_ITEMS) {
        return Promise.reject(new Errors.BulkActionLimitError(GROUP_ACTION_MAX_ITEMS, data.id.length));
    }
    data.id.sort();
    try {
        await uModel.update(
            { status: data.status },
            { where: { entityId: entity.id, id: { [Op.in]: data.id } } }
        );
    } catch (error) {
        return Promise.reject(new Errors.BulkActionDbError());
    }
}

const addCustomDataFilter = (customData: any): object => {
    let parsedJson;
    let filter = {};
    try {
        parsedJson = JSON.parse(customData);
    } catch (e) {
        throw new Errors.ValidationError(`Features filter invalid JSON string - ${customData}`);
    }
    const includesKeys = Object.keys(parsedJson).reduce((result, key) => {
        if (CUSTOM_DATA_FILTER_KEYS.includes(key)) {
            result.push(key);
        }
        return result;
    }, []);
    filter = includesKeys.reduce((result, key) => {
        const keyFromJson = parsedJson[key];
        if (keyFromJson === null || keyFromJson === undefined) {
            result = {
                ...result,
                [key]: {
                    [Op.eq]: keyFromJson
                }
            };
        } else if (typeof keyFromJson === "object") {
            result = {
                ...result,
                [key]: keyFromJson

            };
        } else {
            result = {
                ...result,
                [key]: {
                    [Op.iLike]: keyFromJson
                }
            };
        }
        return result;
    }, {});
    return filter;
};

export async function search(keyEntity: BaseEntity,
                             entityToSearchUnder: BaseEntity,
                             query: WhereOptions<any> = {},
                             customDataQuery?: any): Promise<DetailedUserInfo[]> {

    let entityQuery: WhereOptions<any> = { path: { [Op.like]: entityToSearchUnder.path + "%" } };
    let rolesQuery: WhereOptions<any>;

    if (customDataQuery) {
        query["customData"] = addCustomDataFilter(customDataQuery);
    }
    if (query["entity"]) {
        entityQuery = Object.assign(entityQuery, { name: query["entity"] });
        delete query["entity"];
    }

    if (query["userId"]) {
        query["id"] = query["userId"];
        delete query["userId"];
    }

    if (query["roleId"]) {
        rolesQuery = { id: query["roleId"] };
        delete query["roleId"];
    }

    const customData = query["customDataRoleId"] as any;
    if (customData) {
        let operator;
        let roleIds;
        if (Op.in in customData) {
            roleIds = customData[Op.in];
            operator = "IN";
        } else if (Op.notIn in customData) {
            roleIds = customData[Op.notIn];
            operator = "NOT IN";
        }
        // tslint:disable-next-line:quotemark
        (query as any)[Op.and] = literal(`custom_data->'role'->>'id' ${operator} ('${roleIds.join("','")}')`);
        delete query["customDataRoleId"];
    }

    let sortBy = getSortKey(query, sortableKeys, DEFAULT_SORT_KEY);
    if (sortByMapper[sortBy]) {
        sortBy = sortByMapper[sortBy];
    }
    const sortOrder = valueFromQuery(query, "sortOrder") || "ASC";

    if (valueFromQuery(query, "userType", false) === "no_type") {
        query["userType"] = {
            [Op.eq]: null
        };
    }

    return PagingHelper.findAndCountAll(uModel, {
        attributes: {
            include: [
                [literal("COALESCE(first_name || ' ', '') || COALESCE(last_name, '')") as any, "fullName"]
            ],
        },
        where: query,
        order: [[sortBy, sortOrder]],
        offset: valueFromQuery<number>(query, "offset"),
        limit: valueFromQuery(query, "limit"),
        include: [
            {
                association: uModel.associations.roles,
                where: rolesQuery,
            },
            {
                association: uModel.associations.entity,
                where: entityQuery,
            }
        ],
        distinct: true
    }, user => (new UserImpl(user)).toDetailedInfo(entityToSearchUnder));
}

export async function getAllSiblings(keyEntity: BaseEntity): Promise<number[]> {
    const users = await uModel.findAll({
        where: { entityId: keyEntity.id },
        attributes: ["id"],
    });
    return users.map(user => user.id);
}

export async function getAllSiblingsAndSubUsers(keyEntity: BaseEntity): Promise<number[]> {
    const entityQuery: WhereOptions = { path: { [Op.like]: keyEntity.path + "%" } };
    const users = await uModel.findAll({
        include: [
            {
                association: uModel.associations.entity,
                where: entityQuery,
            }
        ],
        attributes: ["id"],
    });
    return users.map(user => user.id);
}

export async function getUser(entityId: number, username: string): Promise<UserModel> {
    const user = await uModel.findOne({
        include: [{ model: rlModel }],
        where: {
            entityId,
            username
        },
    });

    if (!user) {
        this.log.error("User not found");
        return Promise.reject(new Errors.UserOrPasswordDoesntMatch());
    }

    if (user.status === UserStatus.SUSPENDED) {
        return Promise.reject(new Errors.PlayerIsSuspended());
    }

    return user;
}

export async function requestChangeEmail(entity: BaseEntity, username: string, domain: string): Promise<UserInfo> {
    if (entity.isSuspended()) {
        return Promise.reject(new Errors.ParentSuspendedError());
    }
    const entitySettings: EntitySettings = await Settings.getEntitySettings(entity.name);

    const service = getUserService(entity);
    const user = await service.findOne(username, true);

    if (user.isSuspended()) {
        return Promise.reject(new Errors.ParentSuspendedError());
    }

    if (!user.email) {
        return Promise.reject(new Errors.EmailMissingError());
    }

    const resetToken: string = await createResetToken(entity.key, user.username, "changeEmailTokens");

    const emailData: EmailData = {
        subject: entitySettings.emailTemplates.changeEmail.subject,
        fromEmail: entitySettings.emailTemplates.changeEmail.from,
        htmlPart: entitySettings.emailTemplates.changeEmail.html
    };

    await getEmailService().sendEmail([user.email], emailData, {
        username: user.username,
        token: resetToken,
        domain
    });

    return user.toInfo();
}

export async function forceChangeEmail(entity: BaseEntity,
                                       username: string,
                                       data: DirectChangeEmailData): Promise<UserInfo> {

    if (!entity || !username || !data.email) {
        return Promise.reject(new Errors.ValidationError("missing data"));
    }
    if (entity.isSuspended()) {
        return Promise.reject(new Errors.ParentSuspendedError());
    }

    if (await uModel.findOne({
        where: {
            email: data.email,
            entityId: entity.id,
        },
    })) {
        return Promise.reject(new Errors.EmailAlreadyExistError());
    }

    const service = getUserService(entity);
    const user = await service.findOne(username);
    if (user.isSuspended()) {
        return Promise.reject(new Errors.ParentSuspendedError());
    }

    user.email = data.email;
    return user.save();
}

export async function changeEmail(entity: BaseEntity, username: string, data: ChangeEmailData): Promise<UserInfo> {
    if (!entity || !username || !data.token || !data.newEmail) {
        return Promise.reject(new Errors.ValidationError("missing data"));
    }
    if (await uModel.findOne({
        where: {
            email: data.newEmail,
            entityId: entity.id,
        },
    })) {
        return Promise.reject(new Errors.EmailAlreadyExistError());
    }
    if (entity.isSuspended()) {
        return Promise.reject(new Errors.ParentSuspendedError());
    }
    const entitySettings: EntitySettings = await Settings.getEntitySettings(entity.name);

    const service = getUserService(entity);
    const user = await service.findOne(username, true);

    if (user.isSuspended()) {
        return Promise.reject(new Errors.ParentSuspendedError());
    }

    await verifyResetToken(entity.key,
        username,
        "changeEmailTokens",
        data.token,
        "Change email token is expired, please, generate another one");
    const resetToken: string = await createResetToken(entity.key, user.username, "confirmEmailTokens");

    await SecurityService.stashValueForToken<string>(resetToken, data.newEmail);

    const emailData: EmailData = {
        subject: entitySettings.emailTemplates.changeEmail.subject,
        fromEmail: entitySettings.emailTemplates.changeEmail.from,
        htmlPart: entitySettings.emailTemplates.changeEmail.html
    };

    await getEmailService().sendEmail([data.newEmail], emailData, {
        username: user.username,
        token: resetToken,
        domain: data.domain
    });

    return user.toInfo();
}

export async function confirmEmail(entity: BaseEntity, username: string, token: string): Promise<UserInfo> {
    if (!entity || !username || !token) {
        return Promise.reject(new Errors.ValidationError("missing data"));
    }
    if (entity.isSuspended()) {
        return Promise.reject(new Errors.ParentSuspendedError());
    }

    const service = getUserService(entity);
    const user = await service.findOne(username);

    await verifyResetToken(entity.key, username, "confirmEmailTokens", token, "Change email token is expired, please, generate another one");

    user.email = await SecurityService.unstashValueForToken(token);
    return user.save();
}
