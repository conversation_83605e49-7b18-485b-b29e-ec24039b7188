import { lazy } from "@skywind-group/sw-utils";
import { factory } from "factory-girl";
import { FACTORY } from "./common";
import { GameGroupModel, get } from "../../skywind/models/gamegroup";
import { get as getGameGroupLimitModel } from "../../skywind/models/gamegrouplimit";
import { decorateGetAttributeMethod } from "./entity";
import { GameGroupFilter } from "../../skywind/entities/gamegroup";
import { getGameGroupFilterModel } from "../../skywind/models/gamegroupFilter";

interface GameGroupOptions {
    brandId: number;
    name: string;
    description: string;
    filter: GameGroupFilter;
}

export const defineGameGroupFactory = lazy(() => {
    factory.define(FACTORY.GAME_GROUP, get(), (buildOptions: Partial<GameGroupOptions>) => {
        if (!buildOptions.brandId) {
            buildOptions.brandId = factory.assoc(FACTORY.BRAND, "id");
        }

        return {
            name: factory.chance("guid"),
            description: factory.chance("sentence"),
            ...buildOptions
        };
    }, {
        afterCreate: (instance: GameGroupModel) => {
            return decorateGetAttributeMethod(instance.toJSON());
        }
    });

    factory.define(FACTORY.GAME_GROUP_FILTER, getGameGroupFilterModel(), (buildOptions) => {
        return {
            currencies: [],
            games: [],
            maxTotalBet: 100,
            ...buildOptions
        };
    });

    return factory.define(FACTORY.GAME_GROUP_LIMITS, getGameGroupLimitModel(), (buildOptions) => {
        return {
            limits: {
                "USD": {
                    maxTotalStake: 100,
                    stakeAll: [0.1, 0.5, 1, 2, 3, 5],
                    stakeDef: 1,
                    stakeMax: 5,
                    stakeMin: 0.1,
                    winMax: 20000,
                },
                "CNY": {
                    maxTotalStake: 100,
                    stakeAll: [0.1, 0.5, 1, 2, 3, 50],
                    stakeDef: 1,
                    stakeMax: 50,
                    stakeMin: 0.1,
                    winMax: 20000,
                }
            },
            ...buildOptions
        };
    });
});
