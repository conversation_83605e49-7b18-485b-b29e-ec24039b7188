import * as RoleService from "../../skywind/services/role";
import { listChildrenRoles, listRoles } from "../../skywind/services/role";
import * as EntityService from "../../skywind/services/entity";
import { complexStructure, createComplexStructure, truncate, withTransaction } from "./helper";
import * as Errors from "../../skywind/errors";
import { expect, should } from "chai";
import {
    CreateData as CreateUserData,
    default as getUserService,
    UpdateData as UpdateUserData,
    UserService
} from "../../skywind/services/user/user";
import { CreateData, Role, UpdateData } from "../../skywind/entities/role";
import { factory } from "factory-girl";
import { FACTORY } from "../factories/common";
import { encodeId } from "../../skywind/utils/publicid";

const chai = require("chai");
chai.use(require("chai-shallow-deep-equal"));
chai.use(require("chai-as-promised"));
should();

const ROLE_TITLE = "New role";
const ROLE_DESCRIPTION = "New role description";

describe("Entity Roles", () => {

    before(async () => {
        await truncate();
        await createComplexStructure();

    });

    it("Create role with valid permissions", withTransaction(async () => {

        const PERMISSIONS = ["user"];

        const entity = await factory.create(FACTORY.ENTITY, {});
        const roleData = createRoleData(PERMISSIONS, entity.id);
        roleData.description = ROLE_DESCRIPTION;

        const role = await RoleService.createRole(roleData);
        const id = role.id;

        expect(role.title).equal(ROLE_TITLE);
        expect(role.description).equal(ROLE_DESCRIPTION);
        expect(role.permissions).deep.equal(PERMISSIONS);
        expect(role.entityId).equal(entity.id);
        expect(role.isShared).equal(true);

        expect(role.toInfo()).deep.equal({
            id: id,
            title: ROLE_TITLE,
            description: ROLE_DESCRIPTION,
            owned: undefined,
            ownedBy: undefined
        });

        expect(role.toDetailedInfo())
            .deep
            .equal({
                id: id,
                title: ROLE_TITLE,
                description: ROLE_DESCRIPTION,
                owned: undefined,
                ownedBy: undefined,
                permissions: PERMISSIONS,
                isShared: true
            });
    }));

    it("Create role with invalid permissions - negative", withTransaction(async () => {
        const PERMISSIONS = ["invalid_permission"];

        const entity = await factory.create(FACTORY.ENTITY, {});
        const roleData = createRoleData(PERMISSIONS, entity.id);

        await RoleService.createRole(roleData).should.be.rejectedWith(Errors.PermissionNotExistInList);
    }));

    it("Update role when role is exist", withTransaction(async () => {
        const PERMISSIONS = ["user"];

        const entity = await factory.create(FACTORY.ENTITY, {});
        const roleData = createRoleData(PERMISSIONS, entity.id);

        const createdRole = await RoleService.createRole(roleData);

        const updateData = {
            title: "New title for new role",
            description: "New description for new role",
            permissions: ["user", "payment"],
            entityId: -123456, /* Should not affected on update */
        };

        const updated = await RoleService.updateRole(createdRole.id, entity, updateData as UpdateData);
        updated.permissions.sort();
        expect(updated).deep.equal({
            title: "New title for new role",
            description: "New description for new role",
            permissions: ["user", "payment"].sort(),
            entityId: entity.id,
            isShared: true,
            id: createdRole.id,
        });
    }));

    it("Update isShared property", withTransaction(async () => {

        const PERMISSIONS = ["user"];
        const entity = await factory.create(FACTORY.ENTITY, {});
        const roleData = createRoleData(PERMISSIONS, entity.id);

        const createdRole = await RoleService.createRole(roleData);

        let updateData = {
            isShared: false,
        };

        let updated = await RoleService.updateRole(createdRole.id, entity, updateData as UpdateData);
        updated.permissions.sort();
        expect(updated).deep.equal({
            title: "New role",
            permissions: ["user"],
            entityId: entity.id,
            isShared: false,
            id: createdRole.id,
        });

        updateData = {
            isShared: true,
        };

        updated = await RoleService.updateRole(createdRole.id, entity, updateData as UpdateData);
        updated.permissions.sort();
        expect(updated).deep.equal({
            title: "New role",
            permissions: ["user"],
            entityId: entity.id,
            isShared: true,
            id: createdRole.id,
        });
    }));

    it("Update role with pathTo property", withTransaction(async () => {

        const PERMISSIONS = ["user"];
        const entity = await factory.create(FACTORY.ENTITY);
        const entityTo =  await factory.create(FACTORY.ENTITY);
        const roleData = createRoleData(PERMISSIONS, entity.id);

        const createdRole = await RoleService.createRole(roleData);
        expect(createdRole.entityId).equal(entity.id);

        const updateData = {
            pathTo: entityTo.path,
        };
        const updatedRole = await RoleService.updateRole(createdRole.id, entity, updateData as UpdateData);
        expect(updatedRole.id).equal(createdRole.id);
        expect(updatedRole.entityId).equal(entityTo.id);
    }));

    it("Update role with unknown pathTo - negative", withTransaction(async () => {

        const PERMISSIONS = ["user"];
        const entity = await factory.create(FACTORY.ENTITY);
        const roleData = createRoleData(PERMISSIONS, entity.id);

        const createdRole = await RoleService.createRole(roleData);
        expect(createdRole.entityId).equal(entity.id);

        const updateData = {
            pathTo: "unknown:",
        };
        await RoleService.updateRole(createdRole.id, entity, updateData as UpdateData)
            .should.be.rejectedWith(Errors.EntityCouldNotBeFound);
    }));

    it("Update role with suspended pathTo - negative", withTransaction(async () => {

        const PERMISSIONS = ["user"];
        const masterEntity = await EntityService.findOne({ key: complexStructure.masterKey });
        const entity = await factory.create(FACTORY.ENTITY);
        const entityTo =  await factory.create(FACTORY.ENTITY);
        const roleData = createRoleData(PERMISSIONS, entity.id);

        await EntityService.suspend(masterEntity, { path: entityTo.path });

        const createdRole = await RoleService.createRole(roleData);
        expect(createdRole.entityId).equal(entity.id);

        const updateData = {
            pathTo: entityTo.path,
        };
        await RoleService.updateRole(createdRole.id, entity, updateData as UpdateData)
            .should.be.rejectedWith(Errors.ParentSuspendedError);
    }));

    it("Update child entity's role with pathTo", withTransaction(async () => {
        const PERMISSIONS = ["user"];
        const childEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
        const parentEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        const entityTo = await EntityService.findOne({ key: complexStructure.tle2.key });
        const roleData = createRoleData(PERMISSIONS, childEntity.id);

        const createdRole = await RoleService.createRole(roleData);
        expect(createdRole.entityId).equal(childEntity.id);

        const updateData = {
            pathTo: entityTo.path,
        };
        const updatedRole = await RoleService.updateRole(createdRole.id, parentEntity, updateData as UpdateData);
        expect(updatedRole.id).equal(createdRole.id);
        expect(updatedRole.entityId).equal(entityTo.id);
    }));

    it("Update not owned role with pathTo - negative", withTransaction(async () => {

        const PERMISSIONS = ["user"];
        const entity = await factory.create(FACTORY.ENTITY);
        const entity2 =  await factory.create(FACTORY.ENTITY);
        const entityTo =  await factory.create(FACTORY.ENTITY, {}, { parent: entity2 });
        const roleData = createRoleData(PERMISSIONS, entity.id);

        const createdRole = await RoleService.createRole(roleData);
        expect(createdRole.entityId).equal(entity.id);

        const updateData = {
            pathTo: entityTo.path,
        };
        await RoleService.updateRole(createdRole.id, entity2, updateData as UpdateData)
            .should.be.rejectedWith(Errors.RoleManageFailed);
    }));

    it("Find role by pid when role is exist and entity is exist", withTransaction(async () => {
        const PERMISSIONS = ["user"];
        const entity = await factory.create(FACTORY.ENTITY, {});
        const roleData = createRoleData(PERMISSIONS, entity.id);

        const createdRole = await RoleService.createRole(roleData);
        const foundRole = await RoleService.findOne(createdRole.id, entity);

        expect(createdRole).deep.equal(foundRole);
    }));

    it("Find role by pid when entity exist and role doesn't exist - negative", withTransaction(async () => {
        const entity = await factory.create(FACTORY.ENTITY, {});

        await RoleService.findOne(72058, entity)
            .should.be.rejectedWith(Errors.RoleNotExist);
    }));

    it("Find role by pid when role exist and entity doesn't exist - negative", withTransaction(async () => {
        const PERMISSIONS = ["user"];
        const entity = await factory.create(FACTORY.ENTITY);
        const entity2 = await factory.create(FACTORY.ENTITY);
        const roleData = createRoleData(PERMISSIONS, entity.id);

        const createdRole = await RoleService.createRole(roleData);
        await RoleService.findOne(createdRole.id, entity2)
            .should.be.rejectedWith(Errors.RoleManageFailed);
    }));

    it("Update role when role does't exist - negative", async () => {
        const entity = await factory.create(FACTORY.ENTITY, {});
        const updateData: UpdateData = {
            title: "New title for new role",
            permissions: ["user", "payment"],
            isShared: false,
        } as UpdateData;
        await RoleService.updateRole(10587, entity, updateData)
            .should.be.rejectedWith(Errors.RoleNotExist);
    });

    it("Remove role when role is exist", async () => {
        const PERMISSIONS = ["user"];
        const entity = await factory.create(FACTORY.ENTITY, {});
        const roleData = createRoleData(PERMISSIONS, entity.id);

        const createdRole = await RoleService.createRole(roleData);
        const removedItems = await RoleService.removeRole(createdRole.id, entity, false);

        expect(removedItems).deep.equal(1);

        await RoleService.findOne(createdRole.id, entity).should.be.rejectedWith(Errors.RoleNotExist);
    });

    it("Finds roles", async () => {
        const tle1 = await EntityService.findOne({ key: complexStructure.tle1.key });
        let roleData = createRoleData(["user"], tle1.id, "tle1 - role1");
        const role1ForTle1 = await RoleService.createRole(roleData);
        roleData = createRoleData(["user"], tle1.id, "tle1 - role2", false);
        await RoleService.createRole(roleData);

        const tle1ent1 = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
        roleData = createRoleData(["user"], tle1ent1.id, "tle1ent1 - role1");
        const role1ForEnt1 = await RoleService.createRole(roleData);
        roleData = createRoleData(["user"], tle1ent1.id, "tle1ent1 - role2", false);
        const role2ForEnt1 = await RoleService.createRole(roleData);

        const roles = await RoleService.findRoles(tle1ent1, { entityId: tle1ent1.id });
        const parentRoles = await RoleService.getParentRoles(tle1ent1);
        const result = [...roles, ...parentRoles];
        expect(result.length).equal(3);
        expect(result.sort((a, b) => (a.id > b.id) ? 1 : ((a.id < b.id) ? -1 : 0))
            .map((role: Role) => role.toDetailedInfo()))
            .deep
            .equal([
                {
                    id: role1ForTle1.id,
                    isShared: true,
                    owned: false,
                    ownedBy: "TLE1",
                    permissions: ["user"],
                    title: role1ForTle1.title,
                },
                {
                    id: role1ForEnt1.id,
                    isShared: true,
                    owned: true,
                    ownedBy: "ENT1",
                    permissions: ["user"],
                    title: role1ForEnt1.title,
                },
                {
                    id: role2ForEnt1.id,
                    isShared: false,
                    owned: true,
                    ownedBy: "ENT1",
                    permissions: ["user"],
                    title: role2ForEnt1.title,
                }
            ]);
    });

    it("Finds children roles", async () => {
        const tle1 = await factory.create(FACTORY.ENTITY);
        let roleData = createRoleData(["user"], tle1.id, "tle1 - role1");
        const role1ForTle1 = await RoleService.createRole(roleData);
        roleData = createRoleData(["user"], tle1.id, "tle1 - role2", false);
        const role2ForTle1 = await RoleService.createRole(roleData);

        const tle1ent1 = await factory.create(FACTORY.ENTITY, {}, { parent: tle1 });
        roleData = createRoleData(["user"], tle1ent1.id, "tle1ent1 - role1");
        const role1ForEnt1 = await RoleService.createRole(roleData);
        roleData = createRoleData(["user"], tle1ent1.id, "tle1ent1 - role2", false);
        const role2ForEnt1 = await RoleService.createRole(roleData);

        const tle1ent2 = await factory.create(FACTORY.ENTITY, {}, { parent: tle1 });
        roleData = createRoleData(["user"], tle1ent2.id, "tle1ent2 - role1");
        const roleForEnt2 = await RoleService.createRole(roleData);

        const ownRoles = await RoleService.findRoles(tle1, { entityId: tle1.id });
        const childrenRoles = await RoleService.getChildRoles(tle1);
        const result = [...ownRoles, ...childrenRoles];
        expect(result.length).equal(5);
        expect(result.sort((a, b) => (a.id > b.id) ? 1 : ((a.id < b.id) ? -1 : 0))
            .map((role: Role) => role.toDetailedInfo()))
            .deep
            .equal([
                {
                    id: role1ForTle1.id,
                    isShared: true,
                    owned: true,
                    ownedBy: tle1.name,
                    permissions: ["user"],
                    title: role1ForTle1.title,
                },
                {
                    id: role2ForTle1.id,
                    isShared: false,
                    owned: true,
                    ownedBy: tle1.name,
                    permissions: ["user"],
                    title: role2ForTle1.title,
                },
                {
                    id: role1ForEnt1.id,
                    isShared: true,
                    owned: false,
                    ownedBy: tle1ent1.name,
                    permissions: ["user"],
                    title: role1ForEnt1.title,
                },
                {
                    id: role2ForEnt1.id,
                    isShared: false,
                    owned: false,
                    ownedBy: tle1ent1.name,
                    permissions: ["user"],
                    title: role2ForEnt1.title,
                },
                {
                    id: roleForEnt2.id,
                    isShared: true,
                    owned: false,
                    ownedBy: tle1ent2.name,
                    permissions: ["user"],
                    title: roleForEnt2.title,
                },
            ]);
    });

    it("Finds list roles", withTransaction(async () => {
        const tle1 = await factory.create(FACTORY.ENTITY);
        let roleData = createRoleData(["user"], tle1.id, "tle1 - role1");
        const role1ForTle1 = await RoleService.createRole(roleData);
        roleData = createRoleData(["user"], tle1.id, "tle1 - role2", false);
        const role2ForTle1 = await RoleService.createRole(roleData);

        const tle1ent1 = await factory.create(FACTORY.ENTITY, {}, { parent: tle1 });
        roleData = createRoleData(["user"], tle1ent1.id, "tle1ent1 - role1");
        const role1ForEnt1 = await RoleService.createRole(roleData);
        roleData = createRoleData(["user"], tle1ent1.id, "tle1ent1 - role2", false);
        const role2ForEnt1 = await RoleService.createRole(roleData);

        const tle1ent1BRAND = await factory.create(FACTORY.ENTITY, {}, { parent: tle1ent1 });
        roleData = createRoleData(["user"], tle1ent1BRAND.id, "tle1ent1BRAND - role1");
        const brandRole = await RoleService.createRole(roleData);

        const tle2 = await EntityService.findOne({ key: complexStructure.tle2.key });
        roleData = createRoleData(["user"], tle2.id, "tle2 - role1");
        await RoleService.createRole(roleData);

        const roles = await listRoles(tle1ent1);
        expect(roles.length).equal(4);
        expect(roles.sort((a, b) => (a.id > b.id) ? 1 : ((a.id < b.id) ? -1 : 0)))
            .deep
            .equal([
                {
                    id: role1ForTle1.id,
                    owned: false,
                    ownedBy: tle1.name,
                    title: role1ForTle1.title,
                    isShared: true
                },
                {
                    id: role1ForEnt1.id,
                    owned: true,
                    ownedBy: tle1ent1.name,
                    title: role1ForEnt1.title,
                    isShared: true
                },
                {
                    id: role2ForEnt1.id,
                    owned: true,
                    ownedBy: tle1ent1.name,
                    title: role2ForEnt1.title,
                    isShared: false
                },
                {
                    id: brandRole.id,
                    owned: false,
                    ownedBy: tle1ent1BRAND.name,
                    title: brandRole.title,
                    isShared: true,
                }
            ]);
    }));

    it("Finds list children roles", withTransaction(async () => {
        const tle1 = await factory.create(FACTORY.ENTITY);
        let roleData = createRoleData(["user"], tle1.id, "tle1 - role1");
        const role1ForTle1 = await RoleService.createRole(roleData);
        roleData = createRoleData(["user"], tle1.id, "tle1 - role2", false);
        const role2ForTle1 = await RoleService.createRole(roleData);

        const tle2 = await EntityService.findOne({ key: complexStructure.tle2.key });
        roleData = createRoleData(["user"], tle2.id, "tle2 - role1");
        await RoleService.createRole(roleData);

        const tle1ent1 = await factory.create(FACTORY.ENTITY, {}, { parent: tle1 });
        roleData = createRoleData(["user"], tle1ent1.id, "tle1ent1 - role1");
        const role1ForEnt1 = await RoleService.createRole(roleData);
        roleData = createRoleData(["user"], tle1ent1.id, "tle1ent1 - role2", false);
        const role2ForEnt1 = await RoleService.createRole(roleData);

        const tle1ent1BRAND = await factory.create(FACTORY.ENTITY, {}, { parent: tle1ent1 });
        roleData = createRoleData(["user"], tle1ent1BRAND.id, "tle1ent1BRAND - role1");
        const brandRole = await RoleService.createRole(roleData);

        const roles = await listChildrenRoles(tle1);
        expect(roles.length).equal(5);
        expect(roles.sort((a, b) => (a.id > b.id) ? 1 : ((a.id < b.id) ? -1 : 0)))
            .deep
            .equal([
                {
                    id: role1ForTle1.id,
                    owned: true,
                    ownedBy: tle1.name,
                    title: role1ForTle1.title,
                    isShared: true
                },
                {
                    id: role2ForTle1.id,
                    owned: true,
                    ownedBy: tle1.name,
                    title: role2ForTle1.title,
                    isShared: false
                },
                {
                    id: role1ForEnt1.id,
                    owned: false,
                    ownedBy: tle1ent1.name,
                    title: role1ForEnt1.title,
                    isShared: true
                },
                {
                    id: role2ForEnt1.id,
                    owned: false,
                    ownedBy: tle1ent1.name,
                    title: role2ForEnt1.title,
                    isShared: false
                },
                {
                    id: brandRole.id,
                    owned: false,
                    ownedBy: tle1ent1BRAND.name,
                    title: brandRole.title,
                    isShared: true
                }
            ]);
    }));
});

describe("User Roles", () => {

    let entity;
    let entityUserService: UserService;
    let masterUserService: UserService;

    before(async () => {
        await truncate();
        entity = await factory.create(FACTORY.ENTITY, {});
        entityUserService = getUserService(entity);
        const masterEntity = await EntityService.findOne({ key: complexStructure.masterKey });
        masterUserService = getUserService(masterEntity);
    });

    it("Assign role for user from his entity", async () => {
        const PERMISSIONS = ["user"];
        const roleData = createRoleData(PERMISSIONS, entity.id);

        const createdRole = await RoleService.createRole(roleData);

        const userData = {
            username: "ENT1-USER1",
            password: "password",
            email: "<EMAIL>",
            roles: [{ id: encodeId(createdRole.id), title: "USER_ROLE_TITLE", owned: false }],
            creatorPermissions: {
                grantedPermissions: ["user", "keyentity:user"]
            }
        };
        const user = await entityUserService.create(userData);
        const userInfo = await entityUserService.findOne(user.username);
        expect(userInfo.grantedPermissions).deep.equal(PERMISSIONS);

        const userPermissions = await entityUserService.getPermissionList(user.username);
        expect(userPermissions).deep.equal({ grantedPermissions: PERMISSIONS });
    });

    it("Assign role for user from another entity - negative", async () => {
        const PERMISSIONS = ["user"];
        const hisEntity = await factory.create(FACTORY.ENTITY);
        const tle2Ent1UserService = getUserService(hisEntity);

        const anotherEntity = await factory.create(FACTORY.ENTITY);
        const roleDataForTLE2 = createRoleData(PERMISSIONS, anotherEntity.id);
        const roleForTLE2 = await RoleService.createRole(roleDataForTLE2);

        const userData = {
            username: "ENT1-USER1",
            password: "password",
            email: "<EMAIL>",
            roles: [{ id: encodeId(roleForTLE2.id), title: "USER_ROLE_TITLE", owned: false }]
        };

        await tle2Ent1UserService.create(userData).should.be.rejectedWith(Errors.RoleManageFailed);
    });

    it("Assign duplicated role (same pid) for user - negative", async () => {
        const PERMISSIONS = ["user"];
        const roleData = createRoleData(PERMISSIONS, entity.id);
        const role = await RoleService.createRole(roleData);

        const userData = {
            username: "ENT1-USER1-samePID",
            password: "password",
            email: "<EMAIL>",
            roles: [
                { id: encodeId(role.id), title: "USER_ROLE_TITLE", owned: false },
                { id: encodeId(role.id), title: "USER_ROLE_TITLE_1", owned: false },
            ]
        };

        await entityUserService.create(userData).should.be.rejectedWith(Errors.RoleAddToUserError);
    });

    it("Create user with non existing role - negative", async () => {
        const notExistingRoleId = 1000045;
        const userData = {
            username: "IndraDenim",
            password: "Left!This#Avatar!Before340000births",
            email: "<EMAIL>",
            roles: [
                { id: notExistingRoleId, title: "DO_WHAT_YOU_WANT", owned: false },
            ]
        };
        await masterUserService.create(userData).should.be.rejectedWith(Errors.RoleAddToUserError);
    });

    it("Create user with incorrect role id - negative", async () => {
        const notCorrectRoleId = "d8Sc5c5";
        const userData = {
            username: "IrisMurdoch",
            password: "Left!This#Avatar!Before340000births",
            email: "<EMAIL>",
            roles: [
                { id: 42, title: "DO_WHAT_YOU_WANT", owned: false },
            ]
        };
        const role: any = userData.roles[0];
        role["id"] = notCorrectRoleId;
        await masterUserService.create(userData).should.be.rejectedWith(Errors.RoleAddToUserError);
    });

    it("Assign multiple role (diff pid, same set) for user", async () => {
        const PERMISSIONS_1 = ["user"];
        const PERMISSIONS_2 = ["user"];
        const roleData1 = createRoleData(PERMISSIONS_1, entity.id);
        const roleData2 = createRoleData(PERMISSIONS_2, entity.id);
        const role1 = await RoleService.createRole(roleData1);
        const role2 = await RoleService.createRole(roleData2);

        const userData = {
            username: "ENT1-USER04",
            password: "password",
            email: "<EMAIL>",
            roles: [
                { id: encodeId(role1.id), title: "USER_ROLE_TITLE", owned: false },
                { id: encodeId(role2.id), title: "USER_ROLE_TITLE_1", owned: false },
            ]
        };

        const user = await entityUserService.create(userData);

        const permissions = await entityUserService.getPermissionList(user.username);

        expect(permissions.grantedPermissions).deep.equal(["user"]);
    });

    it("Assign multiple role (diff pid, diff set) for user", async () => {
        const PERMISSIONS_1 = ["user"];
        const PERMISSIONS_2 = ["payment"];
        const roleData1 = createRoleData(PERMISSIONS_1, entity.id);
        const roleData2 = createRoleData(PERMISSIONS_2, entity.id);
        const role1 = await RoleService.createRole(roleData1);
        const role2 = await RoleService.createRole(roleData2);

        const userData = {
            username: "UsrDiffSetDifPid",
            password: "password",
            email: "<EMAIL>",
            roles: [
                { id: encodeId(role1.id), title: "USER_ROLE_TITLE", owned: false },
                { id: encodeId(role2.id), title: "USER_ROLE_TITLE_1", owned: false },
            ]
        };

        const user = await entityUserService.create(userData);

        const permissions = await entityUserService.getPermissionList(user.username);

        expect(permissions.grantedPermissions.sort()).deep.equal(["user", "payment"].sort());
    });

    it("Add user permission via update user info", async () => {
        const PERMISSIONS_1 = ["user"];
        const PERMISSIONS_2 = ["payment"];
        const roleData1 = createRoleData(PERMISSIONS_1, entity.id);
        const roleData2 = createRoleData(PERMISSIONS_2, entity.id);
        const role1 = await RoleService.createRole(roleData1);
        const role2 = await RoleService.createRole(roleData2);

        const userData = {
            username: "ENT1-USER1-01",
            password: "password",
            email: "<EMAIL>",
            roles: [{ id: encodeId(role1.id), title: "USER_ROLE_TITLE", owned: true }],
            creatorPermissions: {
                grantedPermissions: ["user"]
            }
        };

        const user = await entityUserService.create(userData);

        const editedUserData = {
            roles: [
                { id: encodeId(role1.id), title: "USER_ROLE_TITLE", owned: true },
                { id: encodeId(role2.id), title: "USER_ROLE_TITLE_1", owned: true }
            ],
            creatorPermissions: {
                grantedPermissions: ["user", "payment"]
            }
        };

        const editedUser = await entityUserService.update(user.username, editedUserData);
        const permissions = await entityUserService.getPermissionList(editedUser.username);

        expect(permissions.grantedPermissions.sort()).deep.equal(["user", "payment"].sort());
    });

    it("Remove user permission via update user info", async () => {
        const PERMISSIONS_1 = ["user"];
        const PERMISSIONS_2 = ["payment"];
        const roleData1 = createRoleData(PERMISSIONS_1, entity.id);
        const roleData2 = createRoleData(PERMISSIONS_2, entity.id);
        const role1 = await RoleService.createRole(roleData1);
        const role2 = await RoleService.createRole(roleData2);

        const userData = {
            username: "ENT1-USER4",
            password: "password",
            email: "<EMAIL>",
            roles: [{ id: encodeId(role1.id), title: "USER_ROLE_TITLE", owned: true }]
        };

        const user = await entityUserService.create(userData);

        const editedUserData = {
            roles: [{ id: encodeId(role2.id), title: "USER_ROLE_TITLE_1", owned: true }]
        };

        const editedUser = await entityUserService.update(user.username, editedUserData);
        const permissions = await entityUserService.getPermissionList(editedUser.username);

        expect(["payment"]).deep.equal(permissions.grantedPermissions);
    });

    it("Force remove role from entity consequently should be remove from user", async () => {
        const PERMISSIONS = ["user"];
        const roleData = createRoleData(PERMISSIONS, entity.id);

        const role = await RoleService.createRole(roleData);

        const userData = {
            username: "UserWithStringRole",
            password: "password",
            email: "<EMAIL>",
            roles: [{ id: encodeId(role.id), title: "USER_ROLE_TITLE", owned: false }]
        };
        const user = await entityUserService.create(userData);
        const userInfo = await entityUserService.findOne(user.username);
        expect(userInfo.grantedPermissions).deep.equal(PERMISSIONS);

        const removedRoles = await RoleService.removeRole(role.id, entity, true);

        expect(removedRoles).deep.equal(1);

        const userInfoAfterDeletingRole = await entityUserService.findOne(user.username);
        expect(userInfoAfterDeletingRole.grantedPermissions).deep.equal([]);

    });

});

describe("Unshared User Roles", () => {

    let masterEntity;
    let tle1;
    let ent1;
    let tle1UserService: UserService;
    let ent1UserService: UserService;

    before(async () => {
        await truncate();
        await createComplexStructure();
        masterEntity = await EntityService.findOne({ key: complexStructure.masterKey });
        tle1 = await EntityService.findOne({ key: complexStructure.tle1.key });
        ent1 = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
        tle1UserService = getUserService(tle1);
        ent1UserService = getUserService(ent1);
    });

    it("Assigns unshared roles and tries to remove it", async () => {

        const UNSHARED_PERMISSIONS = ["report:games"];
        const UNSHARED_PERMISSIONS_2 = ["report:players"];
        const unsharedRoleData1 = createRoleData(UNSHARED_PERMISSIONS, masterEntity.id, "Unshared role", false);
        const unsharedRole1 = await RoleService.createRole(unsharedRoleData1);
        const unsharedRoleData2 = createRoleData(UNSHARED_PERMISSIONS_2, masterEntity.id, "Unshared role 2", false);
        const unsharedRole2 = await RoleService.createRole(unsharedRoleData2);

        const PERMISSIONS = ["user"];
        const roleData = createRoleData(PERMISSIONS, ent1.id);
        const createdRole = await RoleService.createRole(roleData);

        const userData: CreateUserData = {
            username: "ENT1-USER1",
            password: "password",
            email: "<EMAIL>",
            roles: [
                { id: encodeId(createdRole.id), title: "USER_ROLE_TITLE", owned: true },
                { id: encodeId(unsharedRole1.id), title: "Unshared", owned: false },
            ],
            editorEntity: masterEntity
        };
        const user = await ent1UserService.create(userData);
        const userInfo = await ent1UserService.findOne(user.username);
        expect([...UNSHARED_PERMISSIONS, ...PERMISSIONS]).deep.equal(userInfo.grantedPermissions.sort());

        const editedUserData1: UpdateUserData = {
            roles: [{ id: encodeId(createdRole.id), title: "USER_ROLE_TITLE", owned: true }],
            editorEntity: ent1
        };

        const editedUser1 = await ent1UserService.update(user.username, editedUserData1);
        const editedUserInfo1 = await ent1UserService.findOne(editedUser1.username);
        expect([...UNSHARED_PERMISSIONS, ...PERMISSIONS]).deep.equal(editedUserInfo1.grantedPermissions.sort());

        const editedUserData2: UpdateUserData = {
            roles: [{ id: encodeId(createdRole.id), title: "USER_ROLE_TITLE", owned: true }],
            editorEntity: masterEntity
        };

        const editedUser2 = await ent1UserService.update(user.username, editedUserData2);
        const editedUserInfo2 = await ent1UserService.findOne(editedUser2.username);
        expect(PERMISSIONS).deep.equal(editedUserInfo2.grantedPermissions.sort());
    });
});

describe("User Children Roles", () => {

    let tle1;
    let ent1;
    let tle1UserService: UserService;

    before(async () => {
        await truncate();
        await createComplexStructure();
        tle1 = await EntityService.findOne({ key: complexStructure.tle1.key });
        ent1 = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
        tle1UserService = getUserService(tle1);
    });

    it("Assign role for user from child entity", async () => {
        const PERMISSIONS = ["user"];
        const roleData = createRoleData(PERMISSIONS, ent1.id);
        const createdRole = await RoleService.createRole(roleData);

        const userData: CreateUserData = {
            username: "ENT1-USER1",
            password: "password",
            email: "<EMAIL>",
            roles: [{ id: encodeId(createdRole.id), title: "USER_ROLE_TITLE", owned: false }]
        };
        const user = await tle1UserService.create(userData);
        const userInfo = await tle1UserService.findOne(user.username);
        expect(userInfo.grantedPermissions).deep.equal(PERMISSIONS);

        const userPermissions = await tle1UserService.getPermissionList(user.username);
        expect(userPermissions).deep.equal({ grantedPermissions: PERMISSIONS });
    });

});

describe("No roles update for service without canEdit flag", () => {

    let entity;
    let entityUserService: UserService;
    let masterUserService: UserService;

    before(async () => {
        await truncate();
        entity = await factory.create(FACTORY.ENTITY, {});
        entityUserService = getUserService(entity, false);
        const masterEntity = await EntityService.findOne({ key: complexStructure.masterKey });
        masterUserService = getUserService(masterEntity);
    });

    it("Assign role for user from his entity", async () => {
        const PERMISSIONS = ["user"];
        const roleData = createRoleData(PERMISSIONS, entity.id);

        const createdRole = await RoleService.createRole(roleData);

        const userData = {
            username: "ENT1-USER1",
            password: "password",
            email: "<EMAIL>",
            roles: [{ id: encodeId(createdRole.id), title: "USER_ROLE_TITLE", owned: false }],
            creatorPermissions: {
                grantedPermissions: ["user", "keyentity:user"]
            }
        };
        const user = await entityUserService.create(userData);
        const userInfo = await entityUserService.findOne(user.username);
        expect(userInfo.grantedPermissions).deep.equal([]);

        const userPermissions = await entityUserService.getPermissionList(user.username);
        expect(userPermissions).deep.equal({ grantedPermissions: [] });
    });

    it("Assign multiple role (diff pid, diff set) for user", async () => {
        const PERMISSIONS_1 = ["user"];
        const PERMISSIONS_2 = ["payment"];
        const roleData1 = createRoleData(PERMISSIONS_1, entity.id);
        const roleData2 = createRoleData(PERMISSIONS_2, entity.id);
        const role1 = await RoleService.createRole(roleData1);
        const role2 = await RoleService.createRole(roleData2);

        const userData = {
            username: "UsrDiffSetDifPid",
            password: "password",
            email: "<EMAIL>",
            roles: [
                { id: encodeId(role1.id), title: "USER_ROLE_TITLE", owned: false },
                { id: encodeId(role2.id), title: "USER_ROLE_TITLE_1", owned: false },
            ]
        };

        const user = await entityUserService.create(userData);
        const permissions = await entityUserService.getPermissionList(user.username);

        expect(permissions.grantedPermissions).deep.equal([]);
    });

    it("Add user permission via update user info", async () => {
        const PERMISSIONS_1 = ["user"];
        const PERMISSIONS_2 = ["payment"];
        const roleData1 = createRoleData(PERMISSIONS_1, entity.id);
        const roleData2 = createRoleData(PERMISSIONS_2, entity.id);
        const role1 = await RoleService.createRole(roleData1);
        const role2 = await RoleService.createRole(roleData2);

        const userData = {
            username: "ENT1-USER1-01",
            password: "password",
            email: "<EMAIL>",
            creatorPermissions: {
                grantedPermissions: ["user"]
            }
        };

        const user = await entityUserService.create(userData);

        const editedUserData = {
            roles: [
                { id: encodeId(role1.id), title: "USER_ROLE_TITLE", owned: true },
                { id: encodeId(role2.id), title: "USER_ROLE_TITLE_1", owned: true }
            ]
        };

        const editedUser = await entityUserService.update(user.username, editedUserData);
        const permissions = await entityUserService.getPermissionList(editedUser.username);

        expect(permissions.grantedPermissions).deep.equal([]);
    });

    it("Remove user permission via update user does not change user roles", async () => {
        const PERMISSIONS_1 = ["user"];
        const PERMISSIONS_2 = ["payment"];
        const roleData1 = createRoleData(PERMISSIONS_1, entity.id);
        const roleData2 = createRoleData(PERMISSIONS_2, entity.id);
        const role1 = await RoleService.createRole(roleData1);
        const role2 = await RoleService.createRole(roleData2);

        const userData = {
            username: "ENT1-USER4",
            password: "password",
            email: "<EMAIL>",
            roles: [{ id: encodeId(role1.id), title: "USER_ROLE_TITLE", owned: true }]
        };

        const user = await getUserService(entity).create(userData);

        const editedUserData = {
            roles: [{ id: encodeId(role2.id), title: "USER_ROLE_TITLE_1", owned: true }]
        };

        const editedUser = await entityUserService.update(user.username, editedUserData);
        const permissions = await entityUserService.getPermissionList(editedUser.username);

        expect(["user"]).deep.equal(permissions.grantedPermissions);
    });
});

function createRoleData(permissions: string[], entityId: number,
                        title: string = ROLE_TITLE,
                        isShared: boolean = true): CreateData {
    return {
        title: title,
        permissions: permissions,
        entityId: entityId,
        isShared: isShared,
    };
}
