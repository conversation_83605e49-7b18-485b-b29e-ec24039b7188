import { suite, timeout, test } from "mocha-typescript";
import { BaseApiSuite } from "./base.api";
import { SinonStub, stub } from "sinon";
import { factory } from "factory-girl";
import { FACTORY } from "../factories/common";
import { application } from "../../skywind/server";
import { expect } from "chai";
import * as sinon from "sinon";
import * as request from "request";
import * as superagent from "superagent";
import {
    JackpotAudit,
    JackpotAuditInitiator,
    JackpotAuditType,
    JackpotInstance,
    JackpotTicker
} from "../../skywind/entities/jackpot";
import { dbManager } from "../../skywind/services/jackpot";

@suite("JackpotSpec API", timeout(20000))
class JackpotSpec extends BaseApiSuite {

    public queryDbStub: SinonStub;
    public httpGetStub: SinonStub;
    public httpPhantomGetStub: SinonStub;

    public before() {
        this.queryDbStub = stub(dbManager, "query");
        this.httpGetStub = stub(superagent, "get");
        this.httpPhantomGetStub = stub(request, "get");
    }

    public after() {
        this.queryDbStub.restore();
        this.httpGetStub.restore();
        this.httpPhantomGetStub.restore();
    }

    private parentJackpot = [
        { game_code: "sw_al", jp_id: "SW-MEGA-POT-SHOT" },
        { game_code: "sw_al", jp_id: "SW-SUPER-SHOT" }
    ];

    private phantomJackpots = {
        jackpots: [{ jackpotId: "PHANTOM-GRAND-POT", games: ["sw_al", "sw_mrmnky", "sw_suli"] }]
    };

    private createdAt = new Date();
    private endDate = new Date();
    private externalStartDate = new Date(
        this.createdAt.getFullYear(),
        this.createdAt.getMonth(),
        this.createdAt.getDate(),
        this.createdAt.getHours() + 1
    );

    private jackpotsInstances: JackpotInstance[] = [
        {
            id: "SW-MEGA-POT-SHOT",
            type: "MEGA",
            currency: "EUR",
            isDisabled: false,
            createdAt: this.createdAt,
            endDate: this.endDate,
            info: { externalId: "SW-MEGA-POT-SHOT_HSH1", externalStartDate: this.externalStartDate }
        },
        {
            id: "SW-GRAND-POT-SHOT",
            type: "GRAND",
            currency: "EUR",
            isDisabled: false,
            createdAt: this.createdAt,
            endDate: this.endDate
        },
        {
            id: "SW-SUPER-SHOT",
            type: "SUPER",
            currency: "EUR",
            isDisabled: false,
            createdAt: this.createdAt,
            endDate: this.endDate
        },
        {
            id: "PHANTOM-GRAND-POT",
            type: "ph-1",
            currency: "EUR",
            isDisabled: false,
            createdAt: this.createdAt,
            endDate: this.endDate
        }
    ];

    private jackpotTickers: JackpotTicker[] = [
        {
            jackpotId: "SW-MEGA-POT-SHOT",
            jackpotType: "MEGA",
            currency: "EUR",
            pools: { "1": { amount: 1 } },
            jackpotBaseType: "baseType",
        },
        {
            jackpotId: "SW-GRAND-SHOT",
            jackpotType: "GRAND",
            currency: "EUR",
            pools: { "1": { amount: 1 } },
            jackpotBaseType: "baseType",
        },
        {
            jackpotId: "SW-SUPER-SHOT",
            jackpotType: "SUPER",
            currency: "EUR",
            pools: { "1": { amount: 1 } },
            jackpotBaseType: "baseType",
        },
        {
            jackpotId: "PHANTOM-GRAND-POT",
            jackpotType: "ph-1",
            currency: "EUR",
            pools: { "1": { amount: 1 } },
            jackpotBaseType: "baseType",
        }
    ];

    private jackpotAudits: JackpotAudit[] = [
        {
            jackpotId: "SW-MEGA-POT-SHOT",
            type: JackpotAuditType.CREATE,
            ts: this.createdAt,
            history: { disableMode: 0 },
            initiatorType: JackpotAuditInitiator.USER
        },
        {
            jackpotId: "SW-GRAND-SHOT",
            type: JackpotAuditType.CREATE,
            ts: this.createdAt,
            history: { disableMode: 0 },
            initiatorType: JackpotAuditInitiator.USER
        },
        {
            jackpotId: "SW-SUPER-SHOT",
            type: JackpotAuditType.CREATE,
            ts: this.createdAt,
            history: { disableMode: 0 },
            initiatorType: JackpotAuditInitiator.USER
        },
        {
            jackpotId: "PHANTOM-GRAND-POT",
            type: JackpotAuditType.CREATE,
            ts: this.createdAt,
            history: { disableMode: 0 },
            initiatorType: JackpotAuditInitiator.USER
        }
    ];

    @test
    public async testGetJackpots() {

        this.queryDbStub.resolves([this.parentJackpot]);

        this.httpPhantomGetStub.onCall(0).yields(null, { statusCode: 200 }, this.phantomJackpots);

        const superagentMock: any = {
            set: sinon.stub().returnsThis(),
            timeout: sinon.stub().returnsThis(),
            agent: sinon.stub().returnsThis(),
            query: sinon.stub().returnsThis(),
            catch: sinon.stub().returnsThis()
        };

        const jackpotsInstancesMock: any = {
            then: (callback) => {
                return Promise.resolve(callback({ status: 200, body: this.jackpotsInstances }));
            }
        };
        this.httpGetStub.onCall(0).returns({ ...superagentMock, ...jackpotsInstancesMock });

        const jackpotTickersMock: any = {
            then: (callback) => {
                return Promise.resolve(callback({ status: 200, body: [this.jackpotTickers[0]] }));
            }
        }
        this.httpGetStub.onCall(1).returns({ ...superagentMock, ...jackpotTickersMock });
        const jackpotAuditsMock: any = {
            then: (callback) => {
                return Promise.resolve(callback({ status: 200, body: [this.jackpotAudits[0]] }));
            }
        }
        this.httpGetStub.onCall(2).returns({ ...superagentMock, ...jackpotAuditsMock });
        this.httpGetStub.onCall(3).returns({ ...superagentMock, ...jackpotAuditsMock });

        jackpotTickersMock.then = (callback) => {
            return Promise.resolve(callback({ status: 200, body: [this.jackpotTickers[1]] }));
        };
        this.httpGetStub.onCall(4).returns({ ...superagentMock, ...jackpotTickersMock });
        jackpotAuditsMock.then = (callback) => {
            return Promise.resolve(callback({ status: 200, body: [this.jackpotAudits[1]] }));
        };
        this.httpGetStub.onCall(5).returns({ ...superagentMock, ...jackpotAuditsMock });
        this.httpGetStub.onCall(6).returns({ ...superagentMock, ...jackpotAuditsMock });

        jackpotTickersMock.then = (callback) => {
            return Promise.resolve(callback({ status: 200, body: [this.jackpotTickers[2]] }));
        };
        this.httpGetStub.onCall(7).returns({ ...superagentMock, ...jackpotTickersMock });
        jackpotAuditsMock.then = (callback) => {
            return Promise.resolve(callback({ status: 200, body: [this.jackpotAudits[2]] }));
        };
        this.httpGetStub.onCall(8).returns({ ...superagentMock, ...jackpotAuditsMock });
        this.httpGetStub.onCall(9).returns({ ...superagentMock, ...jackpotAuditsMock });

        jackpotTickersMock.then = (callback) => {
            return Promise.resolve(callback({ status: 200, body: [this.jackpotTickers[3]] }));
        };
        this.httpGetStub.onCall(10).returns({ ...superagentMock, ...jackpotTickersMock });
        jackpotAuditsMock.then = (callback) => {
            return Promise.resolve(callback({ status: 200, body: [this.jackpotAudits[3]] }));
        };
        this.httpGetStub.onCall(11).returns({ ...superagentMock, ...jackpotAuditsMock });
        this.httpGetStub.onCall(12).returns({ ...superagentMock, ...jackpotAuditsMock });

        const brand = await factory.create(FACTORY.BRAND);
        const user = await factory.create(FACTORY.USER, {}, { entityId: brand.id });
        const token = await JackpotSpec.getAccessToken(brand.key,
            user.username,
            ["keyentity:report:jackpot:instances"]);

        const game1 = await factory.create(FACTORY.GAME, {}, { gameCode: "sw_al", code: "sw_al" });
        await factory.create(FACTORY.ENTITY_GAME,
            {},
            {
                entityId: brand.id,
                gameId: game1.id,
                settings: { jackpotId: { "sw-super-shot": "SW-SUPER-SHOT", "sw-grand-pot-shot": "SW-GRAND-POT-SHOT" } }
            });

        const game2 = await factory.create(FACTORY.GAME, {}, { gameCode: "sw_mrmnky", code: "sw_mrmnky" });
        await factory.create(FACTORY.ENTITY_GAME,
            {},
            { entityId: brand.id, gameId: game2.id, settings: { jackpotId: { "sw-super-shot": "SW-SUPER-SHOT" } } });

        const response = await JackpotSpec.request(await application.get())
            .get("/v1/jackpots")
            .query({ gameCodes: "sw_al,sw_mrmnky", jackpotIds: "SW-MEGA-POT-SHOT,SW-SUPER-SHOT,SW-GRAND-POT-SHOT,PHANTOM-GRAND-POT" })
            .set("x-access-token", token);

        expect(response.status).to.be.equal(200);
        expect(response.body).to.be.deep.equal([
                {
                    id: "SW-MEGA-POT-SHOT",
                    name: "SW-MEGA-POT-SHOT",
                    type: "Game Level",
                    startDate: this.createdAt.toISOString(),
                    endDate: this.endDate.toISOString(),
                    status: 1,
                    gameCodes: [
                        "sw_al"
                    ],
                    jackpotPools: [],
                    externalId: "SW-MEGA-POT-SHOT_HSH1",
                    externalStartDate: this.externalStartDate.toISOString(),
                },
                {
                    id: "SW-GRAND-POT-SHOT",
                    name: "SW-GRAND-POT-SHOT",
                    type: "Game Level",
                    startDate: this.createdAt.toISOString(),
                    endDate: this.endDate.toISOString(),
                    status: 1,
                    gameCodes: [
                        "sw_al"
                    ],
                    jackpotPools: [],
                    externalId: "SW-GRAND-POT-SHOT",
                    externalStartDate: this.createdAt.toISOString(),
                },
                {
                    id: "SW-SUPER-SHOT",
                    name: "SW-SUPER-SHOT",
                    type: "Game Level",
                    startDate: this.createdAt.toISOString(),
                    endDate: this.endDate.toISOString(),
                    status: 1,
                    gameCodes: [
                        "sw_al",
                        "sw_mrmnky"
                    ],
                    jackpotPools: [],
                    externalId: "SW-SUPER-SHOT",
                    externalStartDate: this.createdAt.toISOString(),
                },
                {
                    id: "PHANTOM-GRAND-POT",
                    name: "PHANTOM-GRAND-POT",
                    type: "MWJP",
                    startDate: this.createdAt.toISOString(),
                    endDate: this.endDate.toISOString(),
                    status: 1,
                    gameCodes: [
                        "sw_al",
                        "sw_mrmnky"
                    ],
                    jackpotPools: [],
                    externalId: "PHANTOM-GRAND-POT",
                    externalStartDate: this.createdAt.toISOString(),
                }
            ]
        );
    }
}
