import { suite, test, timeout } from "mocha-typescript";
import { expect, should, use } from "chai";

import {
    truncate,
    createComplexStructure
} from "../entities/helper";
import { BaseApiSuite } from "./base.api";
import { application } from "../../skywind/server";
import { publicId } from "@skywind-group/sw-utils";
import * as chaiAsPromised from "chai-as-promised";
import EntityCache from "../../skywind/cache/entity";
import { get as getJurisdictionModel } from "../../skywind/models/jurisdiction";

@suite("JurisdictionSpec", timeout(20000))
class JurisdictionSpec extends BaseApiSuite {

    public static async before() {
        should();
        use(chaiAsPromised);
        await truncate();
        await createComplexStructure();
    }

    public before() {
        EntityCache.reset();
    }

    @test()
    public async findAllEntityJurisdictions() {
        const entity = await JurisdictionSpec.factory.create("Entity");
        const user = await JurisdictionSpec.factory.create("User", {}, {
            entityId: entity.id
        });
        await JurisdictionSpec.factory.createMany("EntityJurisdiction", 2, {}, {
            entityId: entity.id
        });

        const token = await JurisdictionSpec.getAccessToken(
            entity.key, user.username, ["keyentity:jurisdiction:view"]);

        const response = await JurisdictionSpec.request(await application.get())
            .get("/v1/jurisdictions")
            .set("x-access-token", token);

        expect(response.body.length).to.be.equal(2);
    }

    @test()
    public async findOneEntityJurisdictionById() {
        const entity = await JurisdictionSpec.factory.create("Entity");
        const user = await JurisdictionSpec.factory.create("User", {}, {
            entityId: entity.id
        });
        const entityJurisdiction = await JurisdictionSpec.factory.create("EntityJurisdiction", {}, {
            entityId: entity.id
        });
        const jurisdictionInstance = await getJurisdictionModel()
            .findByPk(entityJurisdiction.jurisdictionId);

        const token = await JurisdictionSpec.getAccessToken(
            entity.key, user.username, ["keyentity:jurisdiction:view"]);

        const encodedId = publicId.instance.encode(entityJurisdiction.jurisdictionId);

        const response = await JurisdictionSpec.request(await application.get())
            .get(`/v1/jurisdictions/${jurisdictionInstance.get("code")}`)
            .set("x-access-token", token);

        expect(response.body.id).to.be.equal(encodedId);
    }

    @test()
    public async createJurisdictionByEntity() {
        const entity = await JurisdictionSpec.factory.create("Entity");
        const user = await JurisdictionSpec.factory.create("User", {}, {
            entityId: entity.id
        });

        const token = await JurisdictionSpec.getAccessToken(
            entity.key, user.username, ["keyentity:jurisdiction:create"]);

        const response = await JurisdictionSpec.request(await application.get())
            .post("/v1/jurisdictions")
            .set("x-access-token", token)
            .send({
                title: "sometitle",
                description: "chebureckDescription",
                settings: {
                    someSetting: true
                }
            });

        expect(response.status).to.be.equal(403);
        expect(response.body).to.be.deep.equal({
            message: "Not master entity",
            code: 50
        });
    }

    @test()
    public async updateJurisdictionByEntity() {
        const entity = await JurisdictionSpec.factory.create("Entity");
        const jurisdiction = await JurisdictionSpec.factory.create("Jurisdiction");
        const user = await JurisdictionSpec.factory.create("User", {}, {
            entityId: entity.id
        });

        const token = await JurisdictionSpec.getAccessToken(
            entity.key, user.username, ["keyentity:jurisdiction:edit"]);

        const response = await JurisdictionSpec.request(await application.get())
            .patch(`/v1/jurisdictions/${publicId.instance.encode(jurisdiction.id)}`)
            .set("x-access-token", token)
            .send({
                title: "sometitle",
                description: "chebureckDescription",
                settings: {
                    someSetting: true
                }
            });

        expect(response.status).to.be.equal(403);
        expect(response.body).to.be.deep.equal({
            message: "Not master entity",
            code: 50
        });
    }

    @test()
    public async removeJurisdictionByEntity() {
        const entity = await JurisdictionSpec.factory.create("Entity");
        const jurisdiction = await JurisdictionSpec.factory.create("Jurisdiction");
        const user = await JurisdictionSpec.factory.create("User", {}, {
            entityId: entity.id
        });

        const token = await JurisdictionSpec.getAccessToken(
            entity.key, user.username, ["keyentity:jurisdiction:delete"]);

        const response = await JurisdictionSpec.request(await application.get())
            .delete(`/v1/jurisdictions/${publicId.instance.encode(jurisdiction.id)}`)
            .set("x-access-token", token);

        expect(response.status).to.be.equal(403);
        expect(response.body).to.be.deep.equal({
            message: "Not master entity",
            code: 50
        });
    }
}
