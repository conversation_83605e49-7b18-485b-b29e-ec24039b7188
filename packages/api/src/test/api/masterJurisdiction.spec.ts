import { suite, test, timeout } from "mocha-typescript";
import { expect, should, use } from "chai";
import { application } from "../../skywind/server";
import { publicId } from "@skywind-group/sw-utils";
import { BaseApiSuite } from "./base.api";
import { complexStructure, truncate } from "../entities/helper";
import { VARCHAR_DEFAULT_LENGTH } from "../../skywind/utils/common";
import { get as getJurisdictionModel } from "../../skywind/models/jurisdiction";
const chaiAsPromised = require("chai-as-promised");

@suite(timeout(20000))
class MasterJurisdictionSpec extends BaseApiSuite {

    public static async before() {
        should();
        use(chaiAsPromised);

        await truncate();
    }
    @test()
    public async findAll() {
        await MasterJurisdictionSpec.factory.createMany("Jurisdiction", 3);
        const user = await MasterJurisdictionSpec.factory.create("User");
        const token = await MasterJurisdictionSpec.getAccessToken(
            complexStructure.masterKey, user.username, ["keyentity:jurisdiction:view"]);

        const response = await MasterJurisdictionSpec.request(await application.get())
            .get("/v1/jurisdictions")
            .set("x-access-token", token);

        expect(response.body.length).to.be.equal(4);
    }

    @test()
    public async findOneById() {
        const jurisdiction = await MasterJurisdictionSpec.factory.create("Jurisdiction");
        const user = await MasterJurisdictionSpec.factory.create("User");
        const token = await MasterJurisdictionSpec.getAccessToken(
            complexStructure.masterKey, user.username, ["keyentity:jurisdiction:view"]);

        const encodedId = publicId.instance.encode(jurisdiction.id);

        const jurisdictionInstance = await getJurisdictionModel()
            .findByPk(jurisdiction.id);

        const response = await MasterJurisdictionSpec.request(await application.get())
            .get(`/v1/jurisdictions/${jurisdictionInstance.get("code")}`)
            .set("x-access-token", token);

        expect(response.body.id).to.be.equal(encodedId);
    }

    @test()
    public async create() {
        const user = await MasterJurisdictionSpec.factory.create("User");

        const token = await MasterJurisdictionSpec.getAccessToken(
            complexStructure.masterKey, user.username, ["keyentity:jurisdiction:create"]);

        const title = "chebureckTitle";
        const code = "Check";
        const response = await MasterJurisdictionSpec.request(await application.get())
            .post("/v1/jurisdictions")
            .set("x-access-token", token)
            .send({
                title,
                code,
                description: "chebureckDescription",
                settings: {
                    someSetting: true
                }
            });

        expect(response.status).to.be.equal(201);
        expect(response.body.id).to.be.exist;
        expect(response.body.createdAt).to.be.exist;
        expect(response.body.createdUserId).to.be.equal(publicId.instance.encode(user.id));
        expect(response.body.updatedUserId).to.be.equal(publicId.instance.encode(user.id));
        expect(response.body.title).to.be.equal(title);
        expect(response.body.code).to.be.equal(code);
    }

    @test()
    public async createJurisdictionTwice() {
        const user = await MasterJurisdictionSpec.factory.create("User");
        const jurisdiction = await MasterJurisdictionSpec.factory.create("Jurisdiction");
        const token = await MasterJurisdictionSpec.getAccessToken(
            complexStructure.masterKey, user.username, ["keyentity:jurisdiction:create"]);

        const response = await MasterJurisdictionSpec.request(await application.get())
            .post("/v1/jurisdictions")
            .set("x-access-token", token)
            .send({
                title: jurisdiction.title,
                code: jurisdiction.code,
                description: "chebureckDescription",
                settings: {
                    someSetting: true
                }
            });

        expect(response.status).to.be.equal(400);
        expect(response.body).to.be.deep.equal({
            message: "Validation error: Jurisdiction already exists",
            code: 40
        });

    }

    @test()
    public async createJurisdictionWithLongTitle() {
        const user = await MasterJurisdictionSpec.factory.create("User");
        const token = await MasterJurisdictionSpec.getAccessToken(
            complexStructure.masterKey, user.username, ["keyentity:jurisdiction:create"]);

        const longTitle = "Chebureck".repeat(50);
        const response = await MasterJurisdictionSpec.request(await application.get())
            .post("/v1/jurisdictions")
            .set("x-access-token", token)
            .send({
                title: longTitle,
                description: "chebureckDescription",
                settings: {
                    someSetting: true
                }
            });

        expect(response.status).to.be.equal(400);
        expect(response.body).to.be.deep.equal({
            message: `Validation error: title - max length - ${VARCHAR_DEFAULT_LENGTH} chars`,
            code: 40
        });

    }

    @test()
    public async update() {
        const user = await MasterJurisdictionSpec.factory.create("User");
        const jurisdiction = await MasterJurisdictionSpec.factory.create("Jurisdiction");
        const token = await MasterJurisdictionSpec.getAccessToken(
            complexStructure.masterKey, user.username, ["keyentity:jurisdiction:edit"]);

        const encodedId = publicId.instance.encode(jurisdiction.id);
        const title = "newCheburekovTitle";
        const response = await MasterJurisdictionSpec.request(await application.get())
            .patch(`/v1/jurisdictions/${jurisdiction.code}`)
            .set("x-access-token", token)
            .send({
                title
            });

        expect(response.status).to.be.equal(200);
        expect(response.body.id).to.be.equal(encodedId);
        expect(response.body.title).to.be.equal(title);
    }

    @test()
    public async updateJurisdictionWithLongTitle() {
        const user = await MasterJurisdictionSpec.factory.create("User");
        const jurisdiction = await MasterJurisdictionSpec.factory.create("Jurisdiction");
        const token = await MasterJurisdictionSpec.getAccessToken(
            complexStructure.masterKey, user.username, ["keyentity:jurisdiction:edit"]);

        const encodedId = publicId.instance.encode(jurisdiction.id);
        const title = "newCheburekovTitle".repeat(20);
        const response = await MasterJurisdictionSpec.request(await application.get())
            .patch(`/v1/jurisdictions/${encodedId}`)
            .set("x-access-token", token)
            .send({
                title
            });

        expect(response.status).to.be.equal(400);
        expect(response.body).to.be.deep.equal({
            message: `Validation error: title - max length - ${VARCHAR_DEFAULT_LENGTH} chars`,
            code: 40
        });
    }

    @test()
    public async remove() {
        const user = await MasterJurisdictionSpec.factory.create("User");
        const jurisdiction = await MasterJurisdictionSpec.factory.create("Jurisdiction");
        const token = await MasterJurisdictionSpec.getAccessToken(
            complexStructure.masterKey, user.username, ["keyentity:jurisdiction:delete"]);

        const response = await MasterJurisdictionSpec.request(await application.get())
            .delete(`/v1/jurisdictions/${jurisdiction.code}`)
            .set("x-access-token", token);

        expect(response.status).to.be.equal(204);
    }
}
